--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: root
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO root;

--
-- Name: EventStatus; Type: TYPE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TYPE public."EventStatus" AS ENUM (
    'UPCOMING',
    'CLOSED',
    'COMPLETED'
);


ALTER TYPE public."EventStatus" OWNER TO ucksvgqcubvn1;

--
-- Name: EventType; Type: TYPE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TYPE public."EventType" AS ENUM (
    'RACE',
    'SPRINT'
);


ALTER TYPE public."EventType" OWNER TO ucksvgqcubvn1;

--
-- Name: UserRole; Type: TYPE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TYPE public."UserRole" AS ENUM (
    'ADMIN',
    'PLAYER'
);


ALTER TYPE public."UserRole" OWNER TO ucksvgqcubvn1;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO ucksvgqcubvn1;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.accounts (
    id text NOT NULL,
    "userId" text NOT NULL,
    type text NOT NULL,
    provider text NOT NULL,
    "providerAccountId" text NOT NULL,
    refresh_token text,
    access_token text,
    expires_at integer,
    token_type text,
    scope text,
    id_token text,
    session_state text
);


ALTER TABLE public.accounts OWNER TO ucksvgqcubvn1;

--
-- Name: drivers; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.drivers (
    id text NOT NULL,
    name text NOT NULL,
    team text NOT NULL,
    number integer NOT NULL,
    active boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.drivers OWNER TO ucksvgqcubvn1;

--
-- Name: events; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.events (
    id text NOT NULL,
    name text NOT NULL,
    type public."EventType" NOT NULL,
    date timestamp(3) without time zone NOT NULL,
    "closingDate" timestamp(3) without time zone NOT NULL,
    status public."EventStatus" DEFAULT 'UPCOMING'::public."EventStatus" NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "firstPlaceId" text,
    "secondPlaceId" text,
    "thirdPlaceId" text
);


ALTER TABLE public.events OWNER TO ucksvgqcubvn1;

--
-- Name: predictions; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.predictions (
    id text NOT NULL,
    "userId" text NOT NULL,
    "eventId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "firstPlaceId" text NOT NULL,
    "secondPlaceId" text NOT NULL,
    "thirdPlaceId" text NOT NULL,
    points integer
);


ALTER TABLE public.predictions OWNER TO ucksvgqcubvn1;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.sessions (
    id text NOT NULL,
    "sessionToken" text NOT NULL,
    "userId" text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.sessions OWNER TO ucksvgqcubvn1;

--
-- Name: users; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.users (
    id text NOT NULL,
    name text,
    email text,
    "emailVerified" timestamp(3) without time zone,
    image text,
    role public."UserRole" DEFAULT 'PLAYER'::public."UserRole" NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.users OWNER TO ucksvgqcubvn1;

--
-- Name: verification_tokens; Type: TABLE; Schema: public; Owner: ucksvgqcubvn1
--

CREATE TABLE public.verification_tokens (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.verification_tokens OWNER TO ucksvgqcubvn1;

--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
\.


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.accounts (id, "userId", type, provider, "providerAccountId", refresh_token, access_token, expires_at, token_type, scope, id_token, session_state) FROM stdin;
cmby7bk2r0002pfj4n3gt72ik	cmby7bjyy0000pfj4kl61pw0p	oauth	google	109971343795074845577	\N	********************************************************************************************************************************************************************************************************************************	**********	Bearer	openid https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile	eyJhbGciOiJSUzI1NiIsImtpZCI6IjBkOGE2NzM5OWU3ODgyYWNhZTdkN2Y2OGIyMjgwMjU2YTc5NmE1ODIiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xt2k7F6X3m6pRiDJst4hEucvWZy9ZN6ep91Vdt9E7wV0Q35JWITACVH0cdzHv6TWEVFkl8GnVplR_4Fk_1Kfv6YtCe6ngdwLjI_mdEPORRUOJSuUGE_7-DAPiy3sV2Nbn3tHld6V1p4abzipLKMl4Lxjxf3IfsfIduuK6Ixtlfa4i1rDz8uqWd5kwPb-UOOPS-lKorcXAEmB0xWPybBDiR_sD-0Fv3BrR5sAej436OxNhJJs1nGpuMpvHYtjNwvS2CFzVomLAqxNM08f51J6Ba3Vt6QLZ0uJtz1A8_ElwIBw99g_Vyls_5BdkTx1__jtq8D667RBVofRopaQcwfqxA	\N
cmckkf1pq0002ib04wvv5lhpa	cmckkf1jk0000ib04lnj6x4qj	oauth	google	112530993288256246073	\N	******************************************************************************************************************************************************************************************************************************	1751380214	Bearer	https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile openid	eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg4MjUwM2E1ZmQ1NmU5ZjczNGRmYmE1YzUwZDdiZjQ4ZGIyODRhZTkiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KHlYVzydDGxe02IDsrS3BauM_MfeiimKzXehKIzaKLE9jtDP6b-4vatnq90lVsKBnv5tj2UEfaDe5A0hz5aD4qxKJUiYt_JZLCVinN9bYZNxBuJZPsbmNwiQgoY-KLgQjiKG6aA73KFAaPiEGtutqj0cGZ11wIN53yRoJX6RfKmQQ7B1l2cq_maxELIgRgLKDQ9zBsI5bOXsh8K_T9lmwldoCV038-gEezE7u9J5gA7ULW5-OzBF92hxcpL_Fzaewptj6GE7QZgL5zE11jp5d5LVC8pBlwPOfuRVKjE3Nh6EzWx7XPF282DHrocOHfaocBSISNTTPOXLDeGADGGHfQ	\N
\.


--
-- Data for Name: drivers; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.drivers (id, name, team, number, active, "createdAt", "updatedAt") FROM stdin;
cmcggero90000pfosovbd6s7r	Oscar Piastri	McLaren	81	t	2025-06-28 16:26:59.865	2025-06-28 16:26:59.865
cmcggeruh0001pfosbq06oc07	Lando Norris	McLaren	4	t	2025-06-28 16:27:00.089	2025-06-28 16:27:00.089
cmcggerym0002pfosg73mlkvc	Charles Leclerc	Ferrari	16	t	2025-06-28 16:27:00.238	2025-06-28 16:27:00.238
cmcgges2o0003pfoseeyxo1ot	Lewis Hamilton	Ferrari	44	t	2025-06-28 16:27:00.385	2025-06-28 16:27:00.385
cmcgges6s0004pfos9b8jva0o	Max Verstappen	Red Bull Racing	1	t	2025-06-28 16:27:00.532	2025-06-28 16:27:00.532
cmcggesep0006pfosxgqvpfav	Yuki Tsunoda	Red Bull Racing	22	t	2025-06-28 16:27:00.818	2025-06-28 16:27:00.818
cmcggesis0007pfosxcuv85xe	George Russell	Mercedes	63	t	2025-06-28 16:27:00.964	2025-06-28 16:27:00.964
cmcggesmv0008pfosc82gsqrz	Andrea Kimi Antonelli	Mercedes	12	t	2025-06-28 16:27:01.111	2025-06-28 16:27:01.111
cmcggesqt0009pfosk2s6ujjs	Lance Stroll	Aston Martin	18	t	2025-06-28 16:27:01.253	2025-06-28 16:27:01.253
cmcggesus000apfosoiizl1fy	Fernando Alonso	Aston Martin	14	t	2025-06-28 16:27:01.397	2025-06-28 16:27:01.397
cmcggesys000bpfos1t4yx9c6	Pierre Gasly	Alpine	10	t	2025-06-28 16:27:01.54	2025-06-28 16:27:01.54
cmcgget6v000dpfossiceca71	Franco Colapinto	Alpine	43	t	2025-06-28 16:27:01.832	2025-06-28 16:27:01.832
cmcggetay000epfosl8itzam6	Esteban Ocon	Haas	31	t	2025-06-28 16:27:01.978	2025-06-28 16:27:01.978
cmcggetex000fpfos2n10bawr	Oliver Bearman	Haas	87	t	2025-06-28 16:27:02.122	2025-06-28 16:27:02.122
cmcggetiz000gpfos3x8sqi30	Isack Hadjar	Racing Bulls	6	t	2025-06-28 16:27:02.267	2025-06-28 16:27:02.267
cmcggetmy000hpfosf884c4ox	Alexander Albon	Williams	23	t	2025-06-28 16:27:02.411	2025-06-28 16:27:02.411
cmcggetr0000ipfosy3cx8luj	Carlos Sainz Jr.	Williams	55	t	2025-06-28 16:27:02.557	2025-06-28 16:27:02.557
cmcggetv1000jpfosev9c6uz7	Nico Hulkenberg	Kick Sauber	27	t	2025-06-28 16:27:02.701	2025-06-28 16:27:02.701
cmcggetz3000kpfos729rwzgs	Gabriel Bortoleto	Kick Sauber	5	t	2025-06-28 16:27:02.847	2025-06-28 16:27:02.847
cmcggesaq0005pfos8t3kqztt	Liam Lawson	Red Bull Racing	30	f	2025-06-28 16:27:00.675	2025-07-01 13:28:08.463
cmcgget2u000cpfos0fivfu98	Jack Doohan	Alpine	7	f	2025-06-28 16:27:01.687	2025-07-01 13:28:22.181
\.


--
-- Data for Name: events; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.events (id, name, type, date, "closingDate", status, "createdAt", "updatedAt", "firstPlaceId", "secondPlaceId", "thirdPlaceId") FROM stdin;
cmcggevh0000spfos29uo03nd	Gran Premio del Bahrain 2025	RACE	2025-04-13 17:00:00	2025-04-11 23:59:59	COMPLETED	2025-06-28 16:27:04.788	2025-06-28 16:27:04.788	\N	\N	\N
cmcggevl0000tpfosjfgkjfba	Gran Premio dell'Arabia Saudita 2025	RACE	2025-04-20 19:00:00	2025-04-17 23:59:59	COMPLETED	2025-06-28 16:27:04.933	2025-06-28 16:27:04.933	\N	\N	\N
cmcggevoz000upfosh3o14gyq	Gran Premio di Miami 2025	RACE	2025-05-04 22:00:00	2025-05-02 23:59:59	COMPLETED	2025-06-28 16:27:05.075	2025-06-28 16:27:05.075	\N	\N	\N
cmcggevt2000vpfoswnun6y6l	Sprint Miami 2025	SPRINT	2025-05-04 22:00:00	2025-05-02 23:59:59	COMPLETED	2025-06-28 16:27:05.222	2025-06-28 16:27:05.222	\N	\N	\N
cmcggevx3000wpfosjzkfd5xh	Gran Premio dell'Emilia Romagna 2025	RACE	2025-05-18 15:00:00	2025-05-16 23:59:59	COMPLETED	2025-06-28 16:27:05.368	2025-06-28 16:27:05.368	\N	\N	\N
cmcggew16000xpfoswos6xcen	Gran Premio di Monaco 2025	RACE	2025-05-25 15:00:00	2025-05-23 23:59:59	COMPLETED	2025-06-28 16:27:05.514	2025-06-28 16:27:05.514	\N	\N	\N
cmcggew55000ypfos5yxameg3	Gran Premio di Spagna 2025	RACE	2025-06-01 15:00:00	2025-05-30 23:59:59	COMPLETED	2025-06-28 16:27:05.658	2025-06-28 16:27:05.658	\N	\N	\N
cmcggew97000zpfosyd3clupf	Gran Premio del Canada 2025	RACE	2025-06-15 20:00:00	2025-06-13 23:59:59	COMPLETED	2025-06-28 16:27:05.803	2025-06-28 16:27:05.803	\N	\N	\N
cmcggewd70010pfos9aq4uwxv	Gran Premio d'Austria 2025	RACE	2025-06-29 15:00:00	2025-06-27 23:59:59	UPCOMING	2025-06-28 16:27:05.948	2025-06-28 16:27:05.948	\N	\N	\N
cmcggewh80011pfosmqygwc6f	Gran Premio di Gran Bretagna 2025	RACE	2025-07-06 16:00:00	2025-07-04 23:59:59	UPCOMING	2025-06-28 16:27:06.092	2025-06-28 16:27:06.092	\N	\N	\N
cmcggewl80012pfos11tykkns	Gran Premio del Belgio 2025	RACE	2025-07-27 15:00:00	2025-07-25 23:59:59	UPCOMING	2025-06-28 16:27:06.236	2025-06-28 16:27:06.236	\N	\N	\N
cmcggewpa0013pfos9e8pe95f	Sprint Belgio 2025	SPRINT	2025-07-26 16:00:00	2025-07-25 23:59:59	UPCOMING	2025-06-28 16:27:06.382	2025-06-28 16:27:06.382	\N	\N	\N
cmcggewtb0014pfosybbtp9cc	Gran Premio d'Ungheria 2025	RACE	2025-08-03 15:00:00	2025-08-01 23:59:59	UPCOMING	2025-06-28 16:27:06.528	2025-06-28 16:27:06.528	\N	\N	\N
cmcggewxd0015pfosnp9dt3q6	Gran Premio d'Olanda 2025	RACE	2025-08-31 15:00:00	2025-08-29 23:59:59	UPCOMING	2025-06-28 16:27:06.674	2025-06-28 16:27:06.674	\N	\N	\N
cmcggex1c0016pfosfijhpb6r	Gran Premio d'Italia 2025	RACE	2025-09-07 15:00:00	2025-09-05 23:59:59	UPCOMING	2025-06-28 16:27:06.817	2025-06-28 16:27:06.817	\N	\N	\N
cmcggev90000qpfosldyz9q3c	Sprint Cina 2025	SPRINT	2025-03-22 04:00:00	2025-03-21 23:59:59	COMPLETED	2025-06-28 16:27:04.501	2025-06-28 16:27:04.501	\N	\N	\N
cmcggevd0000rpfos0bduk1b2	Gran Premio del Giappone 2025	RACE	2025-04-06 07:00:00	2025-04-04 22:59:59	COMPLETED	2025-06-28 16:27:04.644	2025-06-28 16:27:04.644	\N	\N	\N
cmcggex5d0017pfos7zegricd	Gran Premio dell'Azerbaijan 2025	RACE	2025-09-21 13:00:00	2025-09-19 23:59:59	UPCOMING	2025-06-28 16:27:06.962	2025-06-28 16:27:06.962	\N	\N	\N
cmcggex9d0018pfospoyl1pnb	Gran Premio di Singapore 2025	RACE	2025-10-05 14:00:00	2025-10-03 23:59:59	UPCOMING	2025-06-28 16:27:07.105	2025-06-28 16:27:07.105	\N	\N	\N
cmcggexde0019pfos3fflljbl	Gran Premio degli Stati Uniti 2025	RACE	2025-10-19 21:00:00	2025-10-17 23:59:59	UPCOMING	2025-06-28 16:27:07.251	2025-06-28 16:27:07.251	\N	\N	\N
cmcggexhn001apfosvwkx006s	Sprint Stati Uniti 2025	SPRINT	2025-10-18 00:00:00	2025-10-17 23:59:59	UPCOMING	2025-06-28 16:27:07.403	2025-06-28 16:27:07.403	\N	\N	\N
cmcggexlp001bpfos18lfn5zl	Gran Premio del Messico 2025	RACE	2025-10-26 21:00:00	2025-10-24 23:59:59	UPCOMING	2025-06-28 16:27:07.549	2025-06-28 16:27:07.549	\N	\N	\N
cmcggexpq001cpfosfq4ex9wb	Gran Premio del Brasile 2025	RACE	2025-11-09 18:00:00	2025-11-07 23:59:59	UPCOMING	2025-06-28 16:27:07.694	2025-06-28 16:27:07.694	\N	\N	\N
cmcggextr001dpfos7g6jtl1v	Sprint Brasile 2025	SPRINT	2025-11-08 19:00:00	2025-11-07 23:59:59	UPCOMING	2025-06-28 16:27:07.839	2025-06-28 16:27:07.839	\N	\N	\N
cmcggexxr001epfosz8voa201	Gran Premio di Las Vegas 2025	RACE	2025-11-22 05:00:00	2025-11-20 23:59:59	UPCOMING	2025-06-28 16:27:07.984	2025-06-28 16:27:07.984	\N	\N	\N
cmcggey1s001fpfos4die30e7	Gran Premio del Qatar 2025	RACE	2025-11-30 17:00:00	2025-11-28 23:59:59	UPCOMING	2025-06-28 16:27:08.129	2025-06-28 16:27:08.129	\N	\N	\N
cmcggey5t001gpfosfpc26f5t	Sprint Qatar 2025	SPRINT	2025-11-29 19:00:00	2025-11-28 23:59:59	UPCOMING	2025-06-28 16:27:08.273	2025-06-28 16:27:08.273	\N	\N	\N
cmcggey9t001hpfos7zcqulao	Gran Premio di Abu Dhabi 2025	RACE	2025-12-07 14:00:00	2025-12-05 23:59:59	UPCOMING	2025-06-28 16:27:08.417	2025-06-28 16:27:08.417	\N	\N	\N
cmcggeuz1000opfoss6xw5xqk	Gran Premio d'Australia 2025	RACE	2025-03-16 05:00:00	2025-03-14 23:59:59	COMPLETED	2025-06-28 16:27:04.141	2025-06-28 16:27:08.851	cmcgges6s0004pfos9b8jva0o	cmcggerym0002pfosg73mlkvc	cmcggeruh0001pfosbq06oc07
cmcggev51000ppfosbgtmpykn	Gran Premio di Cina 2025	RACE	2025-03-23 08:00:00	2025-03-21 23:59:59	COMPLETED	2025-06-28 16:27:04.358	2025-06-28 16:27:09.07	cmcggerym0002pfosg73mlkvc	cmcgges6s0004pfos9b8jva0o	cmcggeruh0001pfosbq06oc07
\.


--
-- Data for Name: predictions; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.predictions (id, "userId", "eventId", "createdAt", "updatedAt", "firstPlaceId", "secondPlaceId", "thirdPlaceId", points) FROM stdin;
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.sessions (id, "sessionToken", "userId", expires) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.users (id, name, email, "emailVerified", image, role, "createdAt", "updatedAt") FROM stdin;
cmby7bjyy0000pfj4kl61pw0p	Stefano Leli	<EMAIL>	\N	https://lh3.googleusercontent.com/a/ACg8ocLNLEIvPamwKf04ivg-ZOlrDh5uFrbkw5AdjXUvftFSJ19dxTQpNg=s96-c	ADMIN	2025-06-15 21:52:42.202	2025-06-15 21:52:56.947
cmckkf1jk0000ib04lnj6x4qj	Carlo Alberto Di Giacinto Ungaretti (CArloa)	<EMAIL>	\N	https://lh3.googleusercontent.com/a/ACg8ocIcL-bP0UBW02j-HjP5Zq94vRdqbB9f8Gj-6omsGO5Qq5ehLBM=s96-c	PLAYER	2025-07-01 13:30:15.824	2025-07-01 13:30:15.824
\.


--
-- Data for Name: verification_tokens; Type: TABLE DATA; Schema: public; Owner: ucksvgqcubvn1
--

COPY public.verification_tokens (identifier, token, expires) FROM stdin;
\.


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: drivers drivers_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.drivers
    ADD CONSTRAINT drivers_pkey PRIMARY KEY (id);


--
-- Name: events events_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);


--
-- Name: predictions predictions_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.predictions
    ADD CONSTRAINT predictions_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: accounts_provider_providerAccountId_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON public.accounts USING btree (provider, "providerAccountId");


--
-- Name: drivers_number_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX drivers_number_key ON public.drivers USING btree (number);


--
-- Name: predictions_userId_eventId_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX "predictions_userId_eventId_key" ON public.predictions USING btree ("userId", "eventId");


--
-- Name: sessions_sessionToken_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX "sessions_sessionToken_key" ON public.sessions USING btree ("sessionToken");


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: verification_tokens_identifier_token_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX verification_tokens_identifier_token_key ON public.verification_tokens USING btree (identifier, token);


--
-- Name: verification_tokens_token_key; Type: INDEX; Schema: public; Owner: ucksvgqcubvn1
--

CREATE UNIQUE INDEX verification_tokens_token_key ON public.verification_tokens USING btree (token);


--
-- Name: accounts accounts_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: events events_firstPlaceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT "events_firstPlaceId_fkey" FOREIGN KEY ("firstPlaceId") REFERENCES public.drivers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: events events_secondPlaceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT "events_secondPlaceId_fkey" FOREIGN KEY ("secondPlaceId") REFERENCES public.drivers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: events events_thirdPlaceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT "events_thirdPlaceId_fkey" FOREIGN KEY ("thirdPlaceId") REFERENCES public.drivers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: predictions predictions_eventId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.predictions
    ADD CONSTRAINT "predictions_eventId_fkey" FOREIGN KEY ("eventId") REFERENCES public.events(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: predictions predictions_firstPlaceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.predictions
    ADD CONSTRAINT "predictions_firstPlaceId_fkey" FOREIGN KEY ("firstPlaceId") REFERENCES public.drivers(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: predictions predictions_secondPlaceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.predictions
    ADD CONSTRAINT "predictions_secondPlaceId_fkey" FOREIGN KEY ("secondPlaceId") REFERENCES public.drivers(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: predictions predictions_thirdPlaceId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.predictions
    ADD CONSTRAINT "predictions_thirdPlaceId_fkey" FOREIGN KEY ("thirdPlaceId") REFERENCES public.drivers(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: predictions predictions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.predictions
    ADD CONSTRAINT "predictions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sessions sessions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: ucksvgqcubvn1
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: root
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO s2cjnixu68lfd;
GRANT ALL ON SCHEMA public TO ucksvgqcubvn1;


--
-- PostgreSQL database dump complete
--

