{"name": "fanta-f1", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "postinstall": "prisma generate", "prisma": "prisma", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force && npm run db:seed", "db:push": "prisma db push && npm run db:seed", "test:drivers": "tsx scripts/test-drivers.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "latest", "next": "latest", "next-auth": "latest", "react": "latest", "react-dom": "latest", "zod": "^3.25.28"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@types/node": "22.15.21", "@types/react": "19.1.5", "autoprefixer": "^10.4.21", "eslint": "latest", "eslint-config-next": "latest", "postcss": "^8.5.3", "prisma": "latest", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "latest"}, "prisma": {"seed": "tsx prisma/seed.ts"}}