import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Risultati delle gare F1 2025 raccolti da ricerche web
const raceResults2025 = [
  {
    eventName: "Gran Premio d'Australia 2025",
    type: "RACE",
    results: {
      first: "<PERSON><PERSON>",
      second: "Max V<PERSON>tappen",
      third: "<PERSON>"
    }
  },
  {
    eventName: "Sprint Cina 2025",
    type: "SPRINT",
    results: {
      first: "<PERSON>",
      second: "<PERSON>",
      third: "Max V<PERSON>ppen"
    }
  },
  {
    eventName: "Gran Premio di Cina 2025",
    type: "RACE",
    results: {
      first: "<PERSON>",
      second: "<PERSON><PERSON> Norris",
      third: "<PERSON>"
    }
  },
  {
    eventName: "Gran Premio del Giappone 2025",
    type: "RACE",
    results: {
      first: "Max Verstappen",
      second: "Lando Norris",
      third: "<PERSON>ri"
    }
  },
  {
    eventName: "Gran Premio del Bahrain 2025",
    type: "RACE",
    results: {
      first: "<PERSON>",
      second: "<PERSON> Russell",
      third: "Land<PERSON> Norris"
    }
  },
  {
    eventName: "Gran Premio dell'Arabia Saudita 2025",
    type: "RACE",
    results: {
      first: "Oscar Piastri",
      second: "Max Verstappen",
      third: "Charles Leclerc"
    }
  },
  {
    eventName: "Gran Premio di Miami 2025",
    type: "RACE",
    results: {
      first: "Oscar Piastri",
      second: "Lando Norris",
      third: "George Russell"
    }
  },
  {
    eventName: "Gran Premio del Canada 2025",
    type: "RACE",
    results: {
      first: "George Russell",
      second: "Max Verstappen",
      third: "Andrea Kimi Antonelli"
    }
  },
  {
    eventName: "Gran Premio dell'Emilia Romagna 2025",
    type: "RACE",
    results: {
      first: "Max Verstappen",
      second: "Lando Norris",
      third: "Oscar Piastri"
    }
  },
  {
    eventName: "Gran Premio di Monaco 2025",
    type: "RACE",
    results: {
      first: "Lando Norris",
      second: "Oscar Piastri",
      third: "Charles Leclerc"
    }
  },
  {
    eventName: "Gran Premio di Spagna 2025",
    type: "RACE",
    results: {
      first: "Oscar Piastri",
      second: "Lando Norris",
      third: "Max Verstappen"
    }
  },
  {
    eventName: "Gran Premio d'Austria 2025",
    type: "RACE",
    results: {
      first: "Lando Norris",
      second: "Oscar Piastri",
      third: "Charles Leclerc"
    }
  }
]

// Mapping dei nomi piloti per gestire variazioni
const driverNameMapping: Record<string, string[]> = {
  "Lewis Hamilton": ["Lewis Hamilton", "HAM"],
  "Oscar Piastri": ["Oscar Piastri", "PIA"],
  "Max Verstappen": ["Max Verstappen", "VER"],
  "Lando Norris": ["Lando Norris", "NOR"],
  "George Russell": ["George Russell", "RUS"],
  "Andrea Kimi Antonelli": ["Kimi Antonelli", "ANT", "Andrea Kimi Antonelli"],
  "Charles Leclerc": ["Charles Leclerc", "LEC"]
}

async function findDriverByName(driverName: string) {
  // Prima prova con il nome esatto
  let driver = await prisma.driver.findFirst({
    where: { 
      name: { equals: driverName, mode: 'insensitive' },
      active: true
    }
  })
  
  if (driver) return driver
  
  // Prova con le variazioni del nome
  const variations = driverNameMapping[driverName] || [driverName]
  
  for (const variation of variations) {
    driver = await prisma.driver.findFirst({
      where: { 
        name: { contains: variation, mode: 'insensitive' },
        active: true
      }
    })
    if (driver) return driver
  }
  
  return null
}

async function findEventByName(eventName: string, type: string) {
  // Prova con il nome esatto
  let event = await prisma.event.findFirst({
    where: { 
      name: { contains: eventName, mode: 'insensitive' },
      type: type as any
    }
  })
  
  if (event) return event
  
  // Prova con variazioni del nome
  const nameVariations = [
    eventName.replace(" Grand Prix", ""),
    eventName.replace("Grand Prix", "GP"),
    eventName.replace(" Sprint", ""),
    eventName + " 2025"
  ]
  
  for (const variation of nameVariations) {
    event = await prisma.event.findFirst({
      where: { 
        name: { contains: variation, mode: 'insensitive' },
        type: type as any
      }
    })
    if (event) return event
  }
  
  return null
}

async function updateEventResults() {
  console.log('🏎️  Aggiornamento risultati F1 2025...\n')
  
  // Prima mostra tutti gli eventi esistenti
  const allEvents = await prisma.event.findMany({
    include: {
      firstPlace: true,
      secondPlace: true,
      thirdPlace: true
    },
    orderBy: { date: 'asc' }
  })
  
  console.log('📋 Eventi esistenti nel database:')
  allEvents.forEach(event => {
    const hasResults = event.firstPlace && event.secondPlace && event.thirdPlace
    console.log(`  - ${event.name} (${event.type}) - ${event.status} ${hasResults ? '✅' : '❌'}`)
  })
  console.log()
  
  // Mostra tutti i piloti attivi
  const allDrivers = await prisma.driver.findMany({
    where: { active: true },
    orderBy: { name: 'asc' }
  })
  
  console.log('👨‍🏁 Piloti attivi nel database:')
  allDrivers.forEach(driver => {
    console.log(`  - ${driver.name} (${driver.team})`)
  })
  console.log()
  
  let updatedCount = 0
  let skippedCount = 0
  
  for (const raceResult of raceResults2025) {
    console.log(`🔍 Processando: ${raceResult.eventName} (${raceResult.type})`)
    
    // Trova l'evento
    const event = await findEventByName(raceResult.eventName, raceResult.type)
    if (!event) {
      console.log(`  ❌ Evento non trovato: ${raceResult.eventName}`)
      skippedCount++
      continue
    }
    
    console.log(`  ✅ Evento trovato: ${event.name}`)
    
    // Verifica se ha già risultati
    if (event.firstPlaceId && event.secondPlaceId && event.thirdPlaceId) {
      console.log(`  ⚠️  Evento già ha risultati, saltando...`)
      skippedCount++
      continue
    }
    
    // Trova i piloti
    const firstDriver = await findDriverByName(raceResult.results.first)
    const secondDriver = await findDriverByName(raceResult.results.second)
    const thirdDriver = await findDriverByName(raceResult.results.third)
    
    if (!firstDriver || !secondDriver || !thirdDriver) {
      console.log(`  ❌ Piloti non trovati:`)
      if (!firstDriver) console.log(`    - 1°: ${raceResult.results.first}`)
      if (!secondDriver) console.log(`    - 2°: ${raceResult.results.second}`)
      if (!thirdDriver) console.log(`    - 3°: ${raceResult.results.third}`)
      skippedCount++
      continue
    }
    
    console.log(`  ✅ Piloti trovati:`)
    console.log(`    - 1°: ${firstDriver.name}`)
    console.log(`    - 2°: ${secondDriver.name}`)
    console.log(`    - 3°: ${thirdDriver.name}`)
    
    // Aggiorna l'evento con i risultati
    try {
      await prisma.event.update({
        where: { id: event.id },
        data: {
          firstPlaceId: firstDriver.id,
          secondPlaceId: secondDriver.id,
          thirdPlaceId: thirdDriver.id,
          status: 'COMPLETED'
        }
      })
      
      console.log(`  ✅ Risultati aggiornati con successo!`)
      updatedCount++
      
    } catch (error) {
      console.log(`  ❌ Errore nell'aggiornamento: ${error}`)
      skippedCount++
    }
    
    console.log()
  }
  
  console.log(`📊 Riepilogo:`)
  console.log(`  - Eventi aggiornati: ${updatedCount}`)
  console.log(`  - Eventi saltati: ${skippedCount}`)
  console.log(`  - Totale processati: ${raceResults2025.length}`)
}

async function main() {
  try {
    await updateEventResults()
  } catch (error) {
    console.error('Errore durante l\'aggiornamento:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
